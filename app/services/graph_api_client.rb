require 'rest-client'

module GraphApiClient
  extend ActiveSupport::Concern

  MAX_CHUNK = 3 * 1024 * 1024

  def create_draft(message)
    url = "#{GRAPH_HOST}/v1.0/me/messages"
    payload = message.to_json

    headers = {
      "Content-type" => "application/json",
      "Authorization" => "Bearer #{@token}",
      "Prefer" => 'IdType="ImmutableId"'
    }

    RestClient.post url, payload, headers
  end

  def create_reply_draft(message_id)
    url = "#{GRAPH_HOST}/v1.0/me/messages/#{message_id}/createReply"

    headers = {
      "Content-type" => "application/json",
      "Authorization" => "Bearer #{@token}",
      "Prefer" => 'IdType="ImmutableId"'
    }

    RestClient.post url, {}, headers
  end

  def create_attachment_upload_session(message_id, file_name, file_size, content_id: nil)
    url = "#{GRAPH_HOST}/v1.0/me/messages/#{message_id}/attachments/createUploadSession"

    headers = {
      "Content-type" => "application/json",
      "Authorization" => "Bearer #{@token}"
    }

    payload = {
      "AttachmentItem": {
        "attachmentType": "file",
        "name": file_name,
        "size": file_size
      }
    }

    payload[:AttachmentItem].merge!({ 'contentId': content_id, 'isInline': true }) if content_id.present?

    res = RestClient.post(url, payload.to_json, headers)
    body = JSON.parse(res.body)
    body['uploadUrl']
  end

  def delete_subscription(subscription_id)
    url = "#{GRAPH_HOST}/v1.0/subscriptions/#{subscription_id}"

    headers = {
      "Content-type" => "application/json",
      "Authorization" => "Bearer #{@token}"
    }

    RestClient.delete url, headers
  end

  def fetch_attachments_for_message(message_id)
    url = "#{GRAPH_HOST}/v1.0/me/messages/#{message_id}/attachments"

    headers = {
      "Content-type" => "application/json",
      "Authorization" => "Bearer #{@token}"
    }

    res = RestClient.get url, headers
    body = JSON.parse(res.body)
    body['value']
  end

  def get_message(message_id)
    url = "#{GRAPH_HOST}/v1.0/me/messages/#{message_id}"

    headers = {
      "Content-type" => "application/json",
      "Authorization" => "Bearer #{@token}"
    }

    res = RestClient.get url, headers
    JSON.parse(res.body)
  end

  def get_subscriptions
    url = "#{GRAPH_HOST}/v1.0/subscriptions"

    headers = {
      "Content-type" => "application/json",
      "Authorization" => "Bearer #{@token}"
    }

    res = RestClient.get url, headers
    body = JSON.parse(res.body)
    body['value']
  end

  def renew_webhook_subscription(subscription_id)
    url = "#{GRAPH_HOST}/v1.0/subscriptions/#{subscription_id}"

    headers = {
      "Content-type" => "application/json",
      "Authorization" => "Bearer #{@token}",
      "Prefer" => 'IdType="ImmutableId"'
    }

    payload = {
      "expirationDateTime": (Time.now + 2880.minutes).utc.strftime('%Y-%m-%dT%H:%M:%S.%LZ')
    }.to_json

    res = RestClient.patch url, payload, headers
    JSON.parse(res.body)
  end

  def subscribe_webhook
    url = "#{GRAPH_HOST}/v1.0/subscriptions?validationToken=kylas"

    headers = {
      "Content-type" => "application/json",
      "Authorization" => "Bearer #{@token}",
      "Prefer" => 'IdType="ImmutableId"'
    }

    payload = {
      "changeType": "created",
      "notificationUrl": API_HOST + '/v1/outlook_webhooks',
      "resource": "me/messages?$filter=isRead eq false",
      "expirationDateTime": (Time.now + 2880.minutes).utc.strftime('%Y-%m-%dT%H:%M:%S.%LZ')
    }.to_json

    res = RestClient.post url, payload, headers
    JSON.parse(res.body)
  end

  def send_draft(message_id)
    url = "#{GRAPH_HOST}/v1.0/me/messages/#{message_id}/send"

    headers = {
      "Content-type" => "application/json",
      "Content-length" => 0,
      "Authorization" => "Bearer #{@token}",
      "Prefer" => 'IdType="ImmutableId"'
    }

    RestClient.post url, {}, headers
  end

  def update_message_draft(message_id, message)
    url = "#{GRAPH_HOST}/v1.0/me/messages/#{message_id}"

    payload = message.to_json

    headers = {
      "Content-type" => "application/json",
      "Authorization" => "Bearer #{@token}"
    }

    res = RestClient.patch url, payload, headers
  end

  def upload_chunk(url, data, file_size, start_pos, end_pos)
    headers = {
      "Content-Type" => "application/octet-stream",
      "Content-Length" => MAX_CHUNK,
      "Content-Range" => "bytes #{start_pos}-#{end_pos}/#{file_size}",
      "Prefer" => 'IdType="ImmutableId"'
    }

    RestClient.put url, data, headers
  end

  def upload_small_attachment(message_id, file_name, data, content_id: nil)
    url = "#{GRAPH_HOST}/v1.0/me/messages/#{message_id}/attachments"

    headers = {
      "Content-type" => "application/json",
      "Authorization" => "Bearer #{@token}"
    }

    payload = {
      "@odata.type": "#microsoft.graph.fileAttachment",
      "name": file_name,
      "contentBytes": data
    }

    payload.merge!({ 'contentId': content_id, 'isInline': true }) if content_id.present?

    RestClient.post(url, payload.to_json, headers)
  end

  def get_raw_message(message_id)
    url = "#{GRAPH_HOST}/v1.0/me/messages/#{message_id}/$value"

    headers = {
      "Content-type" => "application/json",
      "Authorization" => "Bearer #{@token}"
    }

    res = RestClient.get url, headers
    JSON.parse(res.body)
  end
end
