class OutlookBounceDetector
  # # Outlook-specific bounce subject patterns
  # BOUNCE_SUBJECTS = [
  #   /undeliverable/i,
  #   /undelivered.*mail.*returned.*sender/i,
  #   /delivery.*status.*notification/i,
  #   /non[- ]delivery.*report/i,
  #   /failure.*notice/i,               
  #   /message.*not.*delivered/i,
  #   /address.*not.*found/i,
  #   /quota.*exceeded/i,
  #   /exceeded.*storage.*allocation/i,
  #   /relay.*access.*denied/i,
  #   /rejected.*recipient/i,
  #   /delivery.*failure/i
  # ].freeze

  # Hard bounce indicators (permanent failures)
  HARD_BOUNCE_PATTERNS = [
    /5\.\d+\.\d+/,  # 5.x.x SMTP codes
    /550/,          # Permanent failure
    /551/,          # User not local
    /553/,          # Mailbox name not allowed
    /554/,          # Transaction failed
    /delivery has failed/i,
    /the following recipients? cannot be reached/i,
    /no such user/i,
    /mailbox (unavailable|not found)/i,
    /address rejected/i,
    /unrecognized.*recipient/i
    /recipient.*address.*rejected/i,
    /user unknown/i,
    /no such user/i,
    /invalid recipient/i,
    /does not exist/i
  ].freeze

  # Soft bounce indicators (temporary failures)
  SOFT_BOUNCE_PATTERNS = [
    /4\.\d+\.\d+/,  # 4.x.x SMTP codes
    /421/,          # Service not available
    /422/,          # Recipient mailbox full
    /450/,          # Requested action not taken
    /451/,          # Requested action aborted
    /452/,          # Insufficient system storage
    /552/,          # Exceeded storage allocation
    /mailbox.*full/i,
    /quota.*exceeded/i,
    /temporarily.*unavailable/i,
    /try.*again.*later/i,
    /deferred/i,
    /temporary.*failure/i,
    /message.*delayed/i,
    /delivery.*delayed/i
  ].freeze

  # Outlook-specific bounce sender patterns
  BOUNCE_SENDERS = [
    /mailer-daemon/i,
    /postmaster/i,
    /microsoft.*exchange/i,
  ].freeze

  def initialize(message_data, connected_account)
    @message = message_data
    @connected_account = connected_account
    @body = extract_body
    @from = extract_from
    @headers = nil
  end

  def self.call(message_data, connected_account)
    new(message_data, connected_account).detect_bounce
  end

  def detect_bounce
    Rails.logger.info "Starting Bounce detection"
    Rails.logger.info "Bounce Detection: bounce_by_sender? #{bounce_by_sender?} bounce_by_content? #{bounce_by_content?}"

    return { is_bounced: false } unless is_bounce?

    return { is_bounced: false } unless is_bounce_confirmed?

    {
      is_bounced: true,
      bounce_type: determine_bounce_type,
      failed_reason: extract_failed_reason,
      original_message_id: extract_original_message_id
    }
  end

  private

  def is_bounce?
    bounce_by_sender? && bounce_by_content?
  end

  def bounce_by_sender?
    return false unless @from

    BOUNCE_SENDERS.any? { |pattern| @from.match?(pattern) }
  end

  def bounce_by_content?
    return false unless @body

    content = @body.downcase
    (HARD_BOUNCE_PATTERNS + SOFT_BOUNCE_PATTERNS).any? { |pattern| content.match?(pattern) }
  end

  def determine_bounce_type
    content = @body.downcase

    return 'hard' if HARD_BOUNCE_PATTERNS.any? { |pattern| content.match?(pattern) }
    
    return 'soft' if SOFT_BOUNCE_PATTERNS.any? { |pattern| content.match?(pattern) }
    
    'hard'
  end

  def extract_failed_reason
    # First check subject and body for obvious error messages
    content = "#{@subject} #{@body}"
    
    # Look for SMTP error codes and messages
    patterns = [
      /(\d{3}\s+\d\.\d+\.\d+.*)/i,
      /(5\.\d+\.\d+.*)/i,
      /(4\.\d+\.\d+.*)/i,
      /ERROR CODE:\s*(.+)/i,
      /SMTP.*ERROR:\s*(.+)/i
    ]
    
    patterns.each do |pattern|
      match = content.match(pattern)
      return match[1].strip if match
    end

    # If no obvious error found, try to fetch raw headers
    if needs_raw_headers?
      headers = fetch_raw_headers
      if headers
        # Look for bounce-specific headers
        bounce_reason = extract_reason_from_headers(headers)
        return bounce_reason if bounce_reason
      end
    end
    
    # Fallback to first line of body if no specific error found
    @body&.split("\n")&.first&.strip
  end

  def extract_failed_recipient
    content = "#{@subject} #{@body}"
    
    # Look for email addresses in common bounce message patterns
    patterns = [
      /(?:to|recipient):\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/i,
      /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}).*(?:does not exist|not found|rejected|unavailable)/i,
      /X-Failed-Recipients:\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/i,
      /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}),?\s*ERROR CODE/i,
      /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})\s*couldn't be delivered/i
    ]
    
    patterns.each do |pattern|
      match = content.match(pattern)
      return match[1] if match
    end

    # If not found in subject/body, try raw headers
    if needs_raw_headers?
      headers = fetch_raw_headers
      if headers
        failed_recipient = extract_recipient_from_headers(headers)
        return failed_recipient if failed_recipient
      end
    end
    
    nil
  end

  def extract_bounce_details
    {
      provider: 'outlook',
      message_id: @message['id'],
      conversation_id: @message['conversationId'],
      timestamp: @message['receivedDateTime']
    }
  end

  def extract_body
    @message.dig('body', 'content') || ''
  end

  def extract_from
    @message.dig('from', 'emailAddress', 'address')
  end

  def needs_raw_headers?
    # Only fetch raw headers if we couldn't find the information in subject/body
    @subject&.match?(/delivery.*status.*notification/i) || 
    @body&.match?(/smtp|mail.*delivery/i)
  end

  def fetch_raw_headers
    return @headers if @headers

    begin
      # Fetch raw message with headers from Microsoft Graph API
      response = HTTParty.get(
        "https://graph.microsoft.com/v1.0/me/messages/#{@message['id']}/$value",
        headers: {
          'Authorization' => "Bearer #{@connected_account.access_token}",
          'Content-Type' => 'application/json'
        }
      )

      if response.success?
        @headers = parse_raw_headers(response.body)
      end
    rescue => e
      Rails.logger.error "Failed to fetch raw headers for Outlook message: #{e.message}"
    end

    @headers
  end

  def parse_raw_headers(raw_message)
    headers = {}
    lines = raw_message.split("\n")
    
    lines.each do |line|
      break if line.strip.empty? # End of headers
      
      if line.match(/^([^:]+):\s*(.+)$/)
        key = $1.strip.downcase
        value = $2.strip
        headers[key] = value
      end
    end
    
    headers
  end

  def extract_reason_from_headers(headers)
    # Look for bounce-specific headers
    bounce_headers = [
      'x-failed-recipients',
      'x-delivery-status',
      'x-bounce-reason',
      'diagnostic-code'
    ]
    
    bounce_headers.each do |header|
      value = headers[header]
      return value if value
    end
    
    nil
  end

  def extract_recipient_from_headers(headers)
    # Look for failed recipient in headers
    failed_recipient = headers['x-failed-recipients']
    return failed_recipient if failed_recipient
    
    # Parse from diagnostic headers
    diagnostic = headers['diagnostic-code']
    if diagnostic
      match = diagnostic.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/)
      return match[1] if match
    end
    
    nil
  end
end
